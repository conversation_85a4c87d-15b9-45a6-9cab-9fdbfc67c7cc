<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
            <h1 class="h3 fw-bold mb-1">
                <i class="fa fa-edit me-2 text-primary"></i>Modifier un type de paiement
            </h1>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="javascript:void(0)">Administration</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a class="link-fx" wire:click.prevent="goToListPay()" href="javascript:void(0)">Types de paiements</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        Modifier
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <div class="row">
        <!-- Informations générales -->
        <div class="col-md-5">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-info-circle me-1"></i> Informations générales
                    </h3>
                </div>
                <div class="block-content">
                    <form wire:submit.prevent="updatePay" class="space-y-4">
                        <div class="mb-4">
                            <label class="form-label" for="nom">Nom du type de paiement <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('editPay.nom') is-invalid @enderror" 
                                   id="nom" wire:model="editPay.nom" placeholder="Ex: Frais de scolarité">
                            @error('editPay.nom')
                                <div class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" wire:model="editPay.description" 
                                      rows="3" placeholder="Description facultative..."></textarea>
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_mandatory" wire:model="editPay.is_mandatory">
                                <label class="form-check-label" for="is_mandatory">
                                    Paiement obligatoire
                                </label>
                            </div>
                            <div class="form-text text-muted">
                                Cochez si ce type de paiement est obligatoire pour tous les étudiants
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mb-4">
                            <button type="button" wire:click.prevent="goToListPay()" class="btn btn-alt-secondary me-2">
                                <i class="fa fa-arrow-left me-1"></i> Retour
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-check me-1"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Gestion des tarifs -->
        <div class="col-md-7">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        <i class="fa fa-money-bill me-1"></i> Tarifs par niveau
                    </h3>
                </div>
                <div class="block-content">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label" for="annee">Année universitaire <span class="text-danger">*</span></label>
                            <select class="form-select @error('currentAnnee') is-invalid @enderror" 
                                    id="annee" wire:model="currentAnnee">
                                <option value="">Sélectionnez...</option>
                                @foreach($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                            @error('currentAnnee')
                                <div class="invalid-feedback animated fadeIn">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="button" wire:click="replicateFromPreviousYear" class="btn btn-alt-info w-100" 
                                    title="Copier les tarifs d'une autre année" @if(!$currentAnnee) disabled @endif>
                                <i class="fa fa-copy me-1"></i> Copier depuis une autre année
                            </button>
                        </div>
                    </div>

                    <!-- Message d'aide -->
                    <div class="alert alert-info d-flex align-items-center" role="alert">
                        <i class="fa fa-info-circle fa-2x me-3"></i>
                        <div>
                            <strong>À propos des tarifs</strong><br>
                            Sélectionnez une année universitaire, puis cochez les niveaux concernés et définissez les tarifs 
                            correspondants. Cliquez sur "Mettre à jour les tarifs" pour enregistrer vos modifications.
                        </div>
                    </div>

                    @if($currentAnnee)
                        <div class="table-responsive">
                            <table class="table table-hover table-vcenter">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">Actif</th>
                                        <th>Niveau</th>
                                        <th style="width: 200px;">Tarif</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($prix as $index => $pr)
                                        <tr>
                                            <td>
                                                <div class="form-check d-inline-block">
                                                    <input class="form-check-input" type="checkbox" 
                                                           wire:model="prix.{{ $index }}.active" 
                                                           id="niveau_{{ $pr['niveau_id'] }}">
                                                </div>
                                            </td>
                                            <td>
                                                <label class="fw-semibold" for="niveau_{{ $pr['niveau_id'] }}">
                                                    {{ $pr['niveau_nom'] }}
                                                </label>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" 
                                                           wire:model="prix.{{ $index }}.prix" 
                                                           @if(!$pr['active']) disabled @endif>
                                                    <span class="input-group-text">MGA</span>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" wire:click="updateNiveauPrice" class="btn btn-success">
                                <i class="fa fa-check-circle me-1"></i> Mettre à jour les tarifs
                            </button>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fa fa-calendar fa-3x text-muted mb-3"></i>
                            <p>Veuillez sélectionner une année universitaire pour gérer les tarifs</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Modal de réplication -->
            <div wire:ignore.self class="modal fade" id="modal-replicate" tabindex="-1" role="dialog" aria-labelledby="modal-replicate" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Copier les tarifs d'une autre année</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-4">
                                <label class="form-label" for="sourceAnnee">Année source</label>
                                <select class="form-select" id="sourceAnnee" wire:model="sourceAnnee">
                                    <option value="">Sélectionnez...</option>
                                    @foreach($annees as $annee)
                                        @if($annee->id != $currentAnnee)
                                            <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-alt-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" wire:click="replicate" class="btn btn-alt-primary" data-bs-dismiss="modal">
                                <i class="fa fa-copy me-1"></i> Copier
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de réplication -->
@if($showReplicateModal)
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Copier les tarifs</h5>
                <button type="button" class="btn-close" wire:click="$toggle('showReplicateModal')" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <label class="form-label" for="sourceAnnee">Année source</label>
                    <select class="form-select" id="sourceAnnee" wire:model="sourceAnnee">
                        <option value="">Sélectionnez...</option>
                        @foreach($annees as $annee)
                            @if($annee->id != $currentAnnee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endif
                        @endforeach
                    </select>
                    <div class="form-text">
                        Les tarifs seront copiés de cette année vers l'année actuellement sélectionnée.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-alt-secondary" wire:click="$toggle('showReplicateModal')">Annuler</button>
                <button type="button" wire:click="replicate" class="btn btn-alt-primary" data-bs-dismiss="modal">
                    <i class="fa fa-copy me-1"></i> Copier les tarifs
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
@endif