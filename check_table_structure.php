<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Structure de la table niveau_type_payment ===\n\n";

try {
    $columns = DB::select('DESCRIBE niveau_type_payment');
    
    foreach($columns as $col) {
        echo "Colonne: {$col->Field} - Type: {$col->Type} - Null: {$col->Null} - Default: {$col->Default}\n";
    }
    
    echo "\n=== Données existantes ===\n";
    $data = DB::select('SELECT * FROM niveau_type_payment LIMIT 5');
    
    if (empty($data)) {
        echo "Aucune donnée trouvée.\n";
    } else {
        foreach($data as $row) {
            echo "Row: " . json_encode($row) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
