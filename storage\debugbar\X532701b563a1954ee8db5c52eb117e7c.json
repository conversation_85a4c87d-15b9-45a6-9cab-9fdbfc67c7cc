{"__meta": {"id": "X532701b563a1954ee8db5c52eb117e7c", "datetime": "2025-09-12 13:52:27", "utime": 1757674347.242298, "method": "POST", "uri": "/livewire/message/type-pay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757674346.21496, "end": 1757674347.242351, "duration": 1.027390956878662, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1757674346.21496, "relative_start": 0, "end": 1757674346.560494, "relative_end": 1757674346.560494, "duration": 0.3455338478088379, "duration_str": "346ms", "params": [], "collector": null}, {"label": "Application", "start": 1757674346.561073, "relative_start": 0.3461129665374756, "end": 1757674347.242354, "relative_end": 2.86102294921875e-06, "duration": 0.6812808513641357, "duration_str": "681ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26988472, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.typepay.index (\\resources\\views\\livewire\\administration\\typepay\\index.blade.php)", "param_count": 20, "params": ["pays", "annees", "livewireLayout", "errors", "_instance", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/index.blade.php&line=0"}, {"name": "livewire.administration.typepay.liste (\\resources\\views\\livewire\\administration\\typepay\\liste.blade.php)", "param_count": 22, "params": ["__env", "app", "errors", "_instance", "pays", "annees", "livewireLayout", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.027359999999999995, "accumulated_duration_str": "27.36ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00526, "duration_str": "5.26ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 19.225}, {"sql": "select count(*) as aggregate from `type_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 19.225, "width_percent": 2.997}, {"sql": "select * from `type_payments` order by `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 22.222, "width_percent": 2.595}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix`, `niveau_type_payment`.`annee_universitaire_id` as `pivot_annee_universitaire_id` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 24.817, "width_percent": 4.861}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 87}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:87", "connection": "imsaaapp", "start_percent": 29.678, "width_percent": 3.692}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 33.37, "width_percent": 3.436}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 36.806, "width_percent": 4.532}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 41.338, "width_percent": 2.485}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 43.823, "width_percent": 2.595}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 46.418, "width_percent": 2.595}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 49.013, "width_percent": 2.485}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 51.499, "width_percent": 2.522}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 54.02, "width_percent": 2.668}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 56.689, "width_percent": 3.618}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 60.307, "width_percent": 3.034}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 63.341, "width_percent": 2.558}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 65.899, "width_percent": 2.851}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 68.75, "width_percent": 2.887}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 71.637, "width_percent": 2.303}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 73.94, "width_percent": 2.156}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 76.096, "width_percent": 2.303}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 78.399, "width_percent": 2.303}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 80.702, "width_percent": 2.887}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 83.589, "width_percent": 2.705}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 86.294, "width_percent": 3.801}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 90.095, "width_percent": 2.412}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 92.507, "width_percent": 2.595}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 95.102, "width_percent": 2.558}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 97.661, "width_percent": 2.339}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 22, "App\\Models\\Niveau": 77, "App\\Models\\TypePayment": 10, "App\\Models\\User": 1}, "count": 110}, "livewire": {"data": {"type-pay #jQFJKYm8sYWmTtwf7SnZ": "array:5 [\n  \"data\" => array:15 [\n    \"currentPage\" => \"list\"\n    \"newPay\" => []\n    \"editPay\" => []\n    \"currentAnnee\" => null\n    \"sourceAnnee\" => null\n    \"prix\" => []\n    \"searchTerm\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"showReplicateModal\" => false\n    \"showGlobalReplicateModal\" => true\n    \"globalSourceAnnee\" => null\n    \"globalDestinationAnnee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"type-pay\"\n  \"view\" => \"livewire.administration.typepay.index\"\n  \"component\" => \"App\\Http\\Livewire\\TypePay\"\n  \"id\" => \"jQFJKYm8sYWmTtwf7SnZ\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/typepay\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1757674325\n]"}, "request": {"path_info": "/livewire/message/type-pay", "status_code": "<pre class=sf-dump id=sf-dump-1989972043 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1989972043\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-988233843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-988233843\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1352330001 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">jQFJKYm8sYWmTtwf7SnZ</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">type-pay</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">caisse/typepay</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">520215e2</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>newPay</span>\" => []\n      \"<span class=sf-dump-key>editPay</span>\" => []\n      \"<span class=sf-dump-key>currentAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>prix</span>\" => []\n      \"<span class=sf-dump-key>searchTerm</span>\" => \"\"\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showReplicateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showGlobalReplicateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>globalSourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>globalDestinationAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">cfc2f1b3f843004dc9a88484b0c559b5e698da99cbaa786b5dbbd8ae73e758e6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">te1y</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"24 characters\">openGlobalReplicateModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352330001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1192438128 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">694</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik50MFVIaFRSQnhwS3krNDhYQVc1b1E9PSIsInZhbHVlIjoiMGFTVGsxT3ZtN3BPUkwxWEJVWFhpTzIzY2JEOEc2MXIrYmtLU2J1eE5WeE4veTNMRFBtYkV3WlRSTGh4ZWtzWGZqaGNGVVk1Zkh2c053RWxrSHFwS21NdVVUbXMzV2FRZGdSOWFEdGdIVHd4eDBHSVpWS29TdSs0ZTh3QVY5algiLCJtYWMiOiI5NjEwMWU0NzVmNWQxYzVkYTFmZGY1OTg1MzcyNWQ4MjVhMzJhM2IzMzYzNzVjOTM1ZGE0MzA0NDYzMDIwZGUwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImwrTzNpOHlvRHZmaWZKb21XNnoydWc9PSIsInZhbHVlIjoiY050cjIzZTdDbTlUa2tYOExzczY5Tm05L3FpTEo1ekFLTURoQjNaa0JEU3FoWFBiejFRaU1sdFRNSGdpdHVrUmxMOEVURXA1RzJPbFlLbmNKNGp5MVVzY2hYQ0s1UVlVQXErUWhyRnpWMVJwMGZYcWI3VHZab3hIenVFajFEWjUiLCJtYWMiOiIyMmNhODA2YTVjODVmYzdmMWM5YjU3YmEyZDBlZjQxNzBiNzBjYzJlZjJmOTVkMTQ3MGMxMzgxZGI4ZmU3ZDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192438128\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-795781961 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55641</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">694</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">694</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik50MFVIaFRSQnhwS3krNDhYQVc1b1E9PSIsInZhbHVlIjoiMGFTVGsxT3ZtN3BPUkwxWEJVWFhpTzIzY2JEOEc2MXIrYmtLU2J1eE5WeE4veTNMRFBtYkV3WlRSTGh4ZWtzWGZqaGNGVVk1Zkh2c053RWxrSHFwS21NdVVUbXMzV2FRZGdSOWFEdGdIVHd4eDBHSVpWS29TdSs0ZTh3QVY5algiLCJtYWMiOiI5NjEwMWU0NzVmNWQxYzVkYTFmZGY1OTg1MzcyNWQ4MjVhMzJhM2IzMzYzNzVjOTM1ZGE0MzA0NDYzMDIwZGUwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImwrTzNpOHlvRHZmaWZKb21XNnoydWc9PSIsInZhbHVlIjoiY050cjIzZTdDbTlUa2tYOExzczY5Tm05L3FpTEo1ekFLTURoQjNaa0JEU3FoWFBiejFRaU1sdFRNSGdpdHVrUmxMOEVURXA1RzJPbFlLbmNKNGp5MVVzY2hYQ0s1UVlVQXErUWhyRnpWMVJwMGZYcWI3VHZab3hIenVFajFEWjUiLCJtYWMiOiIyMmNhODA2YTVjODVmYzdmMWM5YjU3YmEyZDBlZjQxNzBiNzBjYzJlZjJmOTVkMTQ3MGMxMzgxZGI4ZmU3ZDk4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1757674346.215</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1757674346</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795781961\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-109684947 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2823hTwl4XRMSeNF4WDLtgNNA485HJ3kRlWQ1nyZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109684947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-230097084 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 12 Sep 2025 10:52:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJJbmUxWDBaY2V6cGQxS1UxenBtT1E9PSIsInZhbHVlIjoiR0gzZTk4RVdySnZZTFB0QmFKWTV6NDdZa3hmVzZqTXJRTFlXWVphdXl6eVFRLzFDeTZ6VmdFb3g1NHc4b1N2UjIvNFd3a2U1M0VVMDM1VTdXeVhTUDZRYUNVYXpCblMxMVB2cEFncGR0M3lpRWZ3dkk4d3BVMW12UUN2MmRCcVEiLCJtYWMiOiIzOTllNTMzZWMwYjA4Yzk0YjA4YjU5YTZkMzhlMTQ3MmJmMjI1MGZjMWY5MTUxZDg5MTc0YWRjOTY4ODhlYzUyIiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:52:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkNXT2JvTlpqVmFpMjdEbVRXbDMzZ0E9PSIsInZhbHVlIjoiY3NBVXVjakNlUFBOWjZQbzlucitmaHozUUNqUmFTS1piOUxrQVhPQ0N1dkJNNU1Yc3NmZjVTeEFVUlQ2UjdUTnlWZkNIR1EvbDJFaGp0U1pCTFdHU3M4TDFZTXM4M2puQkR5Y1NidmFsUG9Ic24vSTM1NHV5dENFYlZocXpqdnkiLCJtYWMiOiI5NzMxZDBkOTc5YmQzNGJlMTA3NjQ3ZjE1YjlhMjVlNGRkYTJiZDlhZGM1YzI4NTI3ZGQ5MDkwY2NlNTU2N2IyIiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:52:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJJbmUxWDBaY2V6cGQxS1UxenBtT1E9PSIsInZhbHVlIjoiR0gzZTk4RVdySnZZTFB0QmFKWTV6NDdZa3hmVzZqTXJRTFlXWVphdXl6eVFRLzFDeTZ6VmdFb3g1NHc4b1N2UjIvNFd3a2U1M0VVMDM1VTdXeVhTUDZRYUNVYXpCblMxMVB2cEFncGR0M3lpRWZ3dkk4d3BVMW12UUN2MmRCcVEiLCJtYWMiOiIzOTllNTMzZWMwYjA4Yzk0YjA4YjU5YTZkMzhlMTQ3MmJmMjI1MGZjMWY5MTUxZDg5MTc0YWRjOTY4ODhlYzUyIiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:52:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkNXT2JvTlpqVmFpMjdEbVRXbDMzZ0E9PSIsInZhbHVlIjoiY3NBVXVjakNlUFBOWjZQbzlucitmaHozUUNqUmFTS1piOUxrQVhPQ0N1dkJNNU1Yc3NmZjVTeEFVUlQ2UjdUTnlWZkNIR1EvbDJFaGp0U1pCTFdHU3M4TDFZTXM4M2puQkR5Y1NidmFsUG9Ic24vSTM1NHV5dENFYlZocXpqdnkiLCJtYWMiOiI5NzMxZDBkOTc5YmQzNGJlMTA3NjQ3ZjE1YjlhMjVlNGRkYTJiZDlhZGM1YzI4NTI3ZGQ5MDkwY2NlNTU2N2IyIiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:52:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230097084\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1023939726 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757674325</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023939726\", {\"maxDepth\":0})</script>\n"}}