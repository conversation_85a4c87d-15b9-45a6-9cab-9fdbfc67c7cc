<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\TypePayment;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;

class TypePay extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    // Page constants
    const PAGE_LIST = 'list';
    const PAGE_CREATE = 'create';
    const PAGE_EDIT = 'edit';
    
    // Component state
    public $currentPage = self::PAGE_LIST;
    public $newPay = [];
    public $editPay = [];
    public $currentAnnee;
    public $sourceAnnee;
    public $prix = [];
    public $searchTerm = '';
    public $sortField = 'id';
    public $sortDirection = 'asc';
    
    // Modal states
    public $showReplicateModal = false;
    public $showGlobalReplicateModal = false;
    public $globalSourceAnnee;
    public $globalDestinationAnnee;
    
    protected $queryString = [
        'searchTerm' => ['except' => ''],
        'sortField' => ['except' => 'id'],
        'sortDirection' => ['except' => 'asc'],
    ];
    
    protected $listeners = [
        'delete' => 'deletePay',
        'refreshComponent' => '$refresh'
    ];

    public function mount()
    {
        // Initialisation
        $this->resetPage();
    }

    public function updatingSearchTerm()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function render()
    {
        $query = TypePayment::query()
            ->when($this->searchTerm, function($query) {
                return $query->where('nom', 'like', '%'.$this->searchTerm.'%');
            })
            ->orderBy($this->sortField, $this->sortDirection);
            
        $pays = $query->with(['niveau' => function($query) {
            $query->withPivot('prix', 'annee_universitaire_id');
        }])->paginate(10);
        
        return view('livewire.administration.typepay.index', [
            "pays" => $pays,
            "annees" => AnneeUniversitaire::orderBy('nom', 'desc')->get(),
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function updatedCurrentAnnee()
    {
        if (!empty($this->editPay)) {
            $this->populateNiveau();
        }
    }

    public function rules()
    {
        $rules = [
            'newPay.nom' => 'required|unique:type_payments,nom',
            'newPay.description' => 'nullable|string|max:255',
            'newPay.is_mandatory' => 'boolean',
        ];
        
        if ($this->currentPage === self::PAGE_EDIT) {
            $rules = [
                'editPay.nom' => ['required', Rule::unique('type_payments', 'nom')->ignore($this->editPay['id'] ?? null)],
                'editPay.description' => 'nullable|string|max:255',
                'editPay.is_mandatory' => 'boolean',
            ];
        }
        
        return $rules;
    }

    public function goToListPay()
    {
        $this->currentPage = self::PAGE_LIST;
        $this->resetInputFields();
    }

    public function goToAddPay()
    {
        $this->resetInputFields();
        $this->currentPage = self::PAGE_CREATE;
    }

    public function goToEditPay($id)
    {
        $this->resetInputFields();
        
        $typePay = TypePayment::findOrFail($id);
        $this->editPay = $typePay->toArray();
        
        
            // Otherwise get the latest academic year
            $latestAnnee = AnneeUniversitaire::latest('id')->first();
            if ($latestAnnee) {
                $this->currentAnnee = $latestAnnee->id;
            }
        
        
        $this->populateNiveau();
        $this->currentPage = self::PAGE_EDIT;
    }

    public function populateNiveau()
    {
        $this->prix = [];

        if (empty($this->currentAnnee)) {
            return;
        }

        $niveauIds = [];
        $niveauPrices = [];
        
        if (isset($this->editPay["id"])) {
            $typePayment = TypePayment::find($this->editPay["id"]);
            if ($typePayment) {
                // Get all niveau relationships for this payment type and current year
                $niveauxForCurrentYear = $typePayment->niveau()
                    ->wherePivot('annee_universitaire_id', $this->currentAnnee)
                    ->get();
                
                $niveauIds = $niveauxForCurrentYear->pluck('id')->toArray();
                
                // Create a map of niveau_id => price
                foreach ($niveauxForCurrentYear as $niveau) {
                    $niveauPrices[$niveau->id] = $niveau->pivot->prix;
                }
            }
        }

        // Build the prix array with all available levels
        $niveaux = Niveau::all();
        foreach ($niveaux as $niveau) {
            $isActive = in_array($niveau->id, $niveauIds);
            
            $this->prix[] = [
                "niveau_id" => $niveau->id,
                "niveau_nom" => $niveau->nom,
                "niveau_sigle" => $niveau->sigle ?? $niveau->nom,
                "active" => $isActive,
                "prix" => $isActive ? $niveauPrices[$niveau->id] : 0
            ];
        }
    }

    public function updateNiveauPrice()
    {
        if (!$this->currentAnnee) {
            $this->showError("Veuillez sélectionner une année universitaire.");
            return;
        }
    
        $typePayment = TypePayment::find($this->editPay["id"]);
    
        if (!$typePayment) {
            $this->showError("Type de paiement introuvable.");
            return;
        }

        DB::beginTransaction();
        try {
            // First detach all existing relationships for this year
            $typePayment->niveau()->wherePivot('annee_universitaire_id', $this->currentAnnee)->detach();
            
            // Then attach the active ones
            $attachData = [];
            foreach ($this->prix as $pr) {
                if ($pr['active'] && $pr['prix'] > 0) {
                    $attachData[$pr['niveau_id']] = [
                        'prix' => $pr['prix'],
                        'annee_universitaire_id' => $this->currentAnnee,
                    ];
                }
            }
            
            if (!empty($attachData)) {
                $typePayment->niveau()->attach($attachData);
            }
            
            DB::commit();
            $this->showSuccess("Les tarifs ont été mis à jour avec succès pour l'année universitaire sélectionnée.");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Une erreur est survenue: " . $e->getMessage());
        }
    }

    public function replicateFromPreviousYear()
    {
        $this->showReplicateModal = true;
    }

    public function openGlobalReplicateModal()
    {
        $this->resetGlobalReplicateFields();
        $this->showGlobalReplicateModal = true;
    }

    public function closeGlobalReplicateModal()
    {
        $this->showGlobalReplicateModal = false;
        $this->resetGlobalReplicateFields();
    }

    private function resetGlobalReplicateFields()
    {
        $this->globalSourceAnnee = null;
        $this->globalDestinationAnnee = null;
    }

    public function replicate()
    {
        if (!$this->sourceAnnee) {
            $this->showError("Veuillez sélectionner une année source.");
            return;
        }

        if (!$this->currentAnnee) {
            $this->showError("Veuillez sélectionner une année de destination.");
            return;
        }
    
        $typePayment = TypePayment::find($this->editPay["id"]);
    
        if (!$typePayment) {
            $this->showError("Type de paiement introuvable.");
            return;
        }

        DB::beginTransaction();
        try {
            // Get tariffs from source year
            $sourceTariffs = $typePayment->niveau()
                ->wherePivot('annee_universitaire_id', $this->sourceAnnee)
                ->get();
            
            if ($sourceTariffs->isEmpty()) {
                DB::rollBack();
                $this->showError("Aucun tarif trouvé pour l'année source sélectionnée.");
                return;
            }

            // First detach all existing relationships for the destination year
            $typePayment->niveau()->wherePivot('annee_universitaire_id', $this->currentAnnee)->detach();
            
            // Copy tariffs from source to destination
            $attachData = [];
            foreach ($sourceTariffs as $niveau) {
                $attachData[$niveau->id] = [
                    'prix' => $niveau->pivot->prix,
                    'annee_universitaire_id' => $this->currentAnnee,
                ];
            }
            
            if (!empty($attachData)) {
                $typePayment->niveau()->attach($attachData);
            }
            
            DB::commit();
            
            // Refresh the form
            $this->populateNiveau();
            $this->showReplicateModal = false;
            $this->sourceAnnee = null;
            
            $this->showSuccess("Les tarifs ont été copiés avec succès depuis l'année source.");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Une erreur est survenue: " . $e->getMessage());
        }
    }

    public function replicateAllTariffs()
    {
        if (!$this->globalSourceAnnee) {
            $this->showError("Veuillez sélectionner une année source.");
            return;
        }

        if (!$this->globalDestinationAnnee) {
            $this->showError("Veuillez sélectionner une année de destination.");
            return;
        }

        if ($this->globalSourceAnnee == $this->globalDestinationAnnee) {
            $this->showError("L'année source et l'année de destination doivent être différentes.");
            return;
        }

        DB::beginTransaction();
        try {
            // Get all type payments that have tariffs for the source year
            $typePayments = TypePayment::whereHas('niveau', function($query) {
                $query->wherePivot('annee_universitaire_id', $this->globalSourceAnnee);
            })->get();

            if ($typePayments->isEmpty()) {
                DB::rollBack();
                $this->showError("Aucun tarif trouvé pour l'année source sélectionnée.");
                return;
            }

            $copiedCount = 0;
            $totalTariffs = 0;

            foreach ($typePayments as $typePayment) {
                // Get tariffs from source year for this payment type
                $sourceTariffs = $typePayment->niveau()
                    ->wherePivot('annee_universitaire_id', $this->globalSourceAnnee)
                    ->get();

                if ($sourceTariffs->isNotEmpty()) {
                    // First detach all existing relationships for the destination year
                    $typePayment->niveau()->wherePivot('annee_universitaire_id', $this->globalDestinationAnnee)->detach();

                    // Copy tariffs from source to destination
                    $attachData = [];
                    foreach ($sourceTariffs as $niveau) {
                        $attachData[$niveau->id] = [
                            'prix' => $niveau->pivot->prix,
                            'annee_universitaire_id' => $this->globalDestinationAnnee,
                        ];
                        $totalTariffs++;
                    }

                    if (!empty($attachData)) {
                        $typePayment->niveau()->attach($attachData);
                        $copiedCount++;
                    }
                }
            }

            DB::commit();

            // Close modal and reset fields
            $this->closeGlobalReplicateModal();

            $sourceAnneeNom = AnneeUniversitaire::find($this->globalSourceAnnee)->nom ?? 'Année source';
            $destinationAnneeNom = AnneeUniversitaire::find($this->globalDestinationAnnee)->nom ?? 'Année destination';

            $this->showSuccess("Copie réussie ! {$totalTariffs} tarifs de {$copiedCount} types de paiement ont été copiés de '{$sourceAnneeNom}' vers '{$destinationAnneeNom}'.");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Une erreur est survenue: " . $e->getMessage());
        }
    }

    public function addPay()
    {
        $validationAttributes = $this->validate();

        // Set default values for non-required fields
        if (!isset($validationAttributes["newPay"]["is_mandatory"])) {
            $validationAttributes["newPay"]["is_mandatory"] = false;
        }

        DB::beginTransaction();
        try {
            TypePayment::create($validationAttributes["newPay"]);
            DB::commit();
            
            $this->showSuccess("Le type de paiement a été créé avec succès!");
            $this->goToListPay();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Erreur lors de la création: " . $e->getMessage());
        }
    }

    public function updatePay()
    {
        $validationAttributes = $this->validate();

        DB::beginTransaction();
        try {
            $typePayment = TypePayment::find($this->editPay["id"]);
            if (!$typePayment) {
                DB::rollBack();
                $this->showError("Type de paiement introuvable.");
                return;
            }
            
            $typePayment->update($validationAttributes["editPay"]);
            DB::commit();
            
            $this->showSuccess("Le type de paiement a été mis à jour avec succès!");
            $this->goToListPay();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Erreur lors de la mise à jour: " . $e->getMessage());
        }
    }

    public function confirmDelete($id)
    {
        $this->dispatchBrowserEvent('swal:confirm', [
            'title' => 'Êtes-vous sûr?',
            'text' => "Cette action supprimera définitivement ce type de paiement.",
            'icon' => 'warning',
            'showCancelButton' => true,
            'confirmButtonText' => 'Oui, supprimer',
            'cancelButtonText' => 'Annuler',
            'id' => $id
        ]);
    }

    public function deletePay($id)
    {
        DB::beginTransaction();
        try {
            $typePayment = TypePayment::find($id);
            if (!$typePayment) {
                DB::rollBack();
                $this->showError("Type de paiement introuvable.");
                return;
            }
            
            // First check if this payment type is being used
            $paymentUsageCount = $typePayment->niveau()->count();
            
            if ($paymentUsageCount > 0) {
                // We could either prevent deletion or proceed with detaching
                // Let's detach and then delete for better UX
                $typePayment->niveau()->detach();
            }
            
            $typePayment->delete();
            DB::commit();
            
            $this->showSuccess("Le type de paiement a été supprimé avec succès!");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->showError("Erreur lors de la suppression: " . $e->getMessage());
        }
    }

    public function activateAllNiveaux()
    {
        foreach ($this->prix as $index => $pr) {
            $this->prix[$index]['active'] = true;
        }
    }

    public function deactivateAllNiveaux()
    {
        foreach ($this->prix as $index => $pr) {
            $this->prix[$index]['active'] = false;
        }
    }

    public function applyPriceToAll($price = 0)
    {
        if (!is_numeric($price) || $price < 0) {
            $this->showError("Veuillez entrer un prix valide.");
            return;
        }
        
        foreach ($this->prix as $index => $pr) {
            if ($this->prix[$index]['active']) {
                $this->prix[$index]['prix'] = $price;
            }
        }
    }

    private function resetInputFields()
    {
        $this->newPay = [
            'nom' => '',
            'description' => '',
            'is_mandatory' => false,
        ];
        $this->editPay = [];
        $this->prix = [];
        $this->currentAnnee = null;
        $this->sourceAnnee = null;
        $this->showReplicateModal = false;
        $this->resetGlobalReplicateFields();
        $this->showGlobalReplicateModal = false;
        $this->resetErrorBag();
        $this->resetValidation();
    }

    private function showSuccess($message)
    {
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => $message]);
    }

    private function showError($message)
    {
        $this->dispatchBrowserEvent("showErrorMessage", ["message" => $message]);
    }
}