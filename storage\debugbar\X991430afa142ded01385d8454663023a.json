{"__meta": {"id": "X991430afa142ded01385d8454663023a", "datetime": "2025-09-12 13:53:36", "utime": 1757674416.692854, "method": "POST", "uri": "/livewire/message/type-pay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757674415.776964, "end": 1757674416.692885, "duration": 0.9159209728240967, "duration_str": "916ms", "measures": [{"label": "Booting", "start": 1757674415.776964, "relative_start": 0, "end": 1757674416.188983, "relative_end": 1757674416.188983, "duration": 0.4120190143585205, "duration_str": "412ms", "params": [], "collector": null}, {"label": "Application", "start": 1757674416.189752, "relative_start": 0.41278815269470215, "end": 1757674416.692888, "relative_end": 3.0994415283203125e-06, "duration": 0.5031359195709229, "duration_str": "503ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26848072, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.typepay.index (\\resources\\views\\livewire\\administration\\typepay\\index.blade.php)", "param_count": 20, "params": ["pays", "annees", "livewireLayout", "errors", "_instance", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/index.blade.php&line=0"}, {"name": "livewire.administration.typepay.liste (\\resources\\views\\livewire\\administration\\typepay\\liste.blade.php)", "param_count": 22, "params": ["__env", "app", "errors", "_instance", "pays", "annees", "livewireLayout", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.06792, "accumulated_duration_str": "67.92ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00443, "duration_str": "4.43ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 6.522}, {"sql": "select count(*) as aggregate from `type_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 6.522, "width_percent": 1.193}, {"sql": "select * from `type_payments` order by `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 7.715, "width_percent": 1.31}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix`, `niveau_type_payment`.`annee_universitaire_id` as `pivot_annee_universitaire_id` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00174, "duration_str": "1.74ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 9.025, "width_percent": 2.562}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 87}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00222, "duration_str": "2.22ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:87", "connection": "imsaaapp", "start_percent": 11.587, "width_percent": 3.269}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00273, "duration_str": "2.73ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 14.856, "width_percent": 4.019}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00164, "duration_str": "1.64ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 18.875, "width_percent": 2.415}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00377, "duration_str": "3.77ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 21.29, "width_percent": 5.551}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 26.84, "width_percent": 2.208}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.01025, "duration_str": "10.25ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 29.049, "width_percent": 15.091}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.005849999999999999, "duration_str": "5.85ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 44.14, "width_percent": 8.613}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00158, "duration_str": "1.58ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 52.753, "width_percent": 2.326}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.005, "duration_str": "5ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 55.08, "width_percent": 7.362}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 62.441, "width_percent": 1.708}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 64.149, "width_percent": 1.281}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 65.43, "width_percent": 1.399}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 66.829, "width_percent": 1.605}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 68.433, "width_percent": 5.065}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 73.498, "width_percent": 1.443}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 74.941, "width_percent": 1.561}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 76.502, "width_percent": 1.634}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 78.136, "width_percent": 1.031}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 79.167, "width_percent": 1.193}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 80.359, "width_percent": 1.899}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00172, "duration_str": "1.72ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 82.259, "width_percent": 2.532}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00413, "duration_str": "4.13ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 84.791, "width_percent": 6.081}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 90.872, "width_percent": 1.06}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.004860000000000001, "duration_str": "4.86ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 91.932, "width_percent": 7.155}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 99.087, "width_percent": 0.913}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 31, "App\\Models\\Niveau": 77, "App\\Models\\TypePayment": 10, "App\\Models\\User": 1}, "count": 119}, "livewire": {"data": {"type-pay #BGMZOUFWD9ezpAegoVhH": "array:5 [\n  \"data\" => array:15 [\n    \"currentPage\" => \"list\"\n    \"newPay\" => []\n    \"editPay\" => []\n    \"currentAnnee\" => null\n    \"sourceAnnee\" => null\n    \"prix\" => []\n    \"searchTerm\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"showReplicateModal\" => false\n    \"showGlobalReplicateModal\" => true\n    \"globalSourceAnnee\" => \"6\"\n    \"globalDestinationAnnee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"type-pay\"\n  \"view\" => \"livewire.administration.typepay.index\"\n  \"component\" => \"App\\Http\\Livewire\\TypePay\"\n  \"id\" => \"BGMZOUFWD9ezpAegoVhH\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/typepay\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1757674325\n]"}, "request": {"path_info": "/livewire/message/type-pay", "status_code": "<pre class=sf-dump id=sf-dump-893831405 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-893831405\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-929021880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-929021880\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1464895786 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">BGMZOUFWD9ezpAegoVhH</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">type-pay</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">caisse/typepay</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">8d69777a</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>newPay</span>\" => []\n      \"<span class=sf-dump-key>editPay</span>\" => []\n      \"<span class=sf-dump-key>currentAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>prix</span>\" => []\n      \"<span class=sf-dump-key>searchTerm</span>\" => \"\"\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showReplicateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showGlobalReplicateModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>globalSourceAnnee</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>globalDestinationAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">300e7b0508be4854ce4696c1fbf5a66cb42b92f447f42bb557dce29d415ee215</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rr4y</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">globalSourceAnnee</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464895786\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-105754364 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">682</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJ6cXEvNmdNRkNmdGtlckI0dW1RTnc9PSIsInZhbHVlIjoiUmxDbFBWaWh0MFlmT3hXdExnb2JnakVjSzhHRExUMmpYVUJhQ0ZKSE0vcW44SEpHQWwxYytac2FVeEtYUHp2UjdhWVhTWDVNK01UcHZ4am03WmVBdFducmZRK203eTRVY1hNMi85WXBlSmpUOWdGenF0ejkyNExnLzU3TWYxK3kiLCJtYWMiOiI1M2UwMDIxMDQ2YWQzNTJlOGM4ZWI0NjE4OGM3NTMwY2JlMTUxZDJjZTIxZGVmOTE5M2RhYzM4NzhmMDE3MThhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlIwbHNTQVRjb1pHYWxoOGJnL1N4L0E9PSIsInZhbHVlIjoidjZqYi9nMlZTU0hZZ1V5TkpodVVheUd5S3hoWGZ6SmZCZmFWNXpqVjQ1ZFZrZE5iNlA3T0ZHMGxDL2tiZnZSKzVINHNFVkVudGROeUhOL2Z2NE12RWorU1FqUEZ4UWk0WGxnaEEySHdvZXI4eFlKbmtQTjZPcWJFUk1FSGE4aHYiLCJtYWMiOiI3MWFlNGE5NGM5ODgzMjZjOWRkZTBiZmExMzZhYmVkNjQwZDVhN2Q5NmY1MmQ0MmM5YTA2MGQxZTVmOTExY2I3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105754364\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1285934685 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55784</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">682</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">682</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJ6cXEvNmdNRkNmdGtlckI0dW1RTnc9PSIsInZhbHVlIjoiUmxDbFBWaWh0MFlmT3hXdExnb2JnakVjSzhHRExUMmpYVUJhQ0ZKSE0vcW44SEpHQWwxYytac2FVeEtYUHp2UjdhWVhTWDVNK01UcHZ4am03WmVBdFducmZRK203eTRVY1hNMi85WXBlSmpUOWdGenF0ejkyNExnLzU3TWYxK3kiLCJtYWMiOiI1M2UwMDIxMDQ2YWQzNTJlOGM4ZWI0NjE4OGM3NTMwY2JlMTUxZDJjZTIxZGVmOTE5M2RhYzM4NzhmMDE3MThhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlIwbHNTQVRjb1pHYWxoOGJnL1N4L0E9PSIsInZhbHVlIjoidjZqYi9nMlZTU0hZZ1V5TkpodVVheUd5S3hoWGZ6SmZCZmFWNXpqVjQ1ZFZrZE5iNlA3T0ZHMGxDL2tiZnZSKzVINHNFVkVudGROeUhOL2Z2NE12RWorU1FqUEZ4UWk0WGxnaEEySHdvZXI4eFlKbmtQTjZPcWJFUk1FSGE4aHYiLCJtYWMiOiI3MWFlNGE5NGM5ODgzMjZjOWRkZTBiZmExMzZhYmVkNjQwZDVhN2Q5NmY1MmQ0MmM5YTA2MGQxZTVmOTExY2I3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1757674415.777</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1757674415</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285934685\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1499458791 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2823hTwl4XRMSeNF4WDLtgNNA485HJ3kRlWQ1nyZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499458791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-991860783 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 12 Sep 2025 10:53:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlF6VVNEQ3QvZmU4RkRhU0NNNFltU1E9PSIsInZhbHVlIjoiajNhQWlaKzJVMCt0d2FGWEVqRG9Yc1hnK3phVWtZQ2xza3FiclliZEx5MVc1WC9XVXh0eExSL1RMREVpQVVHeW9WMlZjUUl1ZXV5WGJBcnFrcHhaN1dBQVJNMlc5cVZIT0NRWTVnaWhiZnRjTDJpelVFU25VK2g1YjJCdXA0R2siLCJtYWMiOiIxYTJhN2FkYmI4MDU4MjgxYjNhZDE0NzcyYTJjZWE4NzA2Y2RhNjY1NDAwYTk0YmIzOGFiYmJiNDE4OWRmMTE2IiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:53:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ik81YTJoSlVwVC9kTCsrTlQrVk5selE9PSIsInZhbHVlIjoiNldXclpmOUhKVzF3RHlCVHlzN2NIRGhVQWlpNGVzQnVFbTFwYW0wMnBEOXRCM2pnTnpvQTFrYW55ZkRxd0lvR2ZsVkFyVUdRN0dqYTk5V3ViZVptc1JPN1oyWFJ4aTVWNG1IdDU4V1BFc1NSZ2tvcFpVOFd2M3pIR1FZMDFDNHciLCJtYWMiOiJjYTgwZDViOGJlZGUwZmNlNWZiOTJkNjgxNmJmNjQ0MDkxOTc2OTkyNGYwNGYwNzFmYTk0N2Q1ZmU5NmUzOTBmIiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:53:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlF6VVNEQ3QvZmU4RkRhU0NNNFltU1E9PSIsInZhbHVlIjoiajNhQWlaKzJVMCt0d2FGWEVqRG9Yc1hnK3phVWtZQ2xza3FiclliZEx5MVc1WC9XVXh0eExSL1RMREVpQVVHeW9WMlZjUUl1ZXV5WGJBcnFrcHhaN1dBQVJNMlc5cVZIT0NRWTVnaWhiZnRjTDJpelVFU25VK2g1YjJCdXA0R2siLCJtYWMiOiIxYTJhN2FkYmI4MDU4MjgxYjNhZDE0NzcyYTJjZWE4NzA2Y2RhNjY1NDAwYTk0YmIzOGFiYmJiNDE4OWRmMTE2IiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:53:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ik81YTJoSlVwVC9kTCsrTlQrVk5selE9PSIsInZhbHVlIjoiNldXclpmOUhKVzF3RHlCVHlzN2NIRGhVQWlpNGVzQnVFbTFwYW0wMnBEOXRCM2pnTnpvQTFrYW55ZkRxd0lvR2ZsVkFyVUdRN0dqYTk5V3ViZVptc1JPN1oyWFJ4aTVWNG1IdDU4V1BFc1NSZ2tvcFpVOFd2M3pIR1FZMDFDNHciLCJtYWMiOiJjYTgwZDViOGJlZGUwZmNlNWZiOTJkNjgxNmJmNjQ0MDkxOTc2OTkyNGYwNGYwNzFmYTk0N2Q1ZmU5NmUzOTBmIiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:53:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991860783\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-542432303 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757674325</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542432303\", {\"maxDepth\":0})</script>\n"}}