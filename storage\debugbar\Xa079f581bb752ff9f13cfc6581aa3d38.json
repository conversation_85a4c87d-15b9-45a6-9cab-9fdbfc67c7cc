{"__meta": {"id": "Xa079f581bb752ff9f13cfc6581aa3d38", "datetime": "2025-09-12 13:54:04", "utime": 1757674444.314816, "method": "POST", "uri": "/livewire/message/type-pay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757674443.217819, "end": 1757674444.314851, "duration": 1.097032070159912, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1757674443.217819, "relative_start": 0, "end": 1757674443.756128, "relative_end": 1757674443.756128, "duration": 0.5383090972900391, "duration_str": "538ms", "params": [], "collector": null}, {"label": "Application", "start": 1757674443.756966, "relative_start": 0.539147138595581, "end": 1757674444.314854, "relative_end": 2.86102294921875e-06, "duration": 0.5578877925872803, "duration_str": "558ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26932120, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.typepay.index (\\resources\\views\\livewire\\administration\\typepay\\index.blade.php)", "param_count": 20, "params": ["pays", "annees", "livewireLayout", "errors", "_instance", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/index.blade.php&line=0"}, {"name": "livewire.administration.typepay.liste (\\resources\\views\\livewire\\administration\\typepay\\liste.blade.php)", "param_count": 22, "params": ["__env", "app", "errors", "_instance", "pays", "annees", "livewireLayout", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.11404, "accumulated_duration_str": "114ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0050999999999999995, "duration_str": "5.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 4.472}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 337}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:337", "connection": "imsaaapp", "start_percent": 4.472, "width_percent": 0}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 391}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:391", "connection": "imsaaapp", "start_percent": 4.472, "width_percent": 0}, {"sql": "select count(*) as aggregate from `type_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 4.472, "width_percent": 0.842}, {"sql": "select * from `type_payments` order by `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0179, "duration_str": "17.9ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 5.314, "width_percent": 15.696}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix`, `niveau_type_payment`.`annee_universitaire_id` as `pivot_annee_universitaire_id` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.011789999999999998, "duration_str": "11.79ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 21.01, "width_percent": 10.338}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 87}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:87", "connection": "imsaaapp", "start_percent": 31.349, "width_percent": 0.921}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 32.269, "width_percent": 0.64}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00257, "duration_str": "2.57ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 32.91, "width_percent": 2.254}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 35.163, "width_percent": 0.614}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 35.777, "width_percent": 0.588}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.008119999999999999, "duration_str": "8.12ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 36.364, "width_percent": 7.12}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 43.485, "width_percent": 1.315}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 44.8, "width_percent": 0.912}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00266, "duration_str": "2.66ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 45.712, "width_percent": 2.333}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 48.045, "width_percent": 0.938}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 48.983, "width_percent": 0.772}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 49.754, "width_percent": 0.833}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 50.588, "width_percent": 0.623}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 51.21, "width_percent": 0.588}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00468, "duration_str": "4.68ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 51.798, "width_percent": 4.104}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 55.901, "width_percent": 0.588}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 56.489, "width_percent": 0.938}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 57.427, "width_percent": 3.016}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00242, "duration_str": "2.42ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 60.444, "width_percent": 2.122}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0071200000000000005, "duration_str": "7.12ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 62.566, "width_percent": 6.243}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.022260000000000002, "duration_str": "22.26ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 68.809, "width_percent": 19.519}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0057599999999999995, "duration_str": "5.76ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 88.329, "width_percent": 5.051}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0019299999999999999, "duration_str": "1.93ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 93.38, "width_percent": 1.692}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 95.072, "width_percent": 1.333}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0040999999999999995, "duration_str": "4.1ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 96.405, "width_percent": 3.595}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 31, "App\\Models\\Niveau": 77, "App\\Models\\TypePayment": 10, "App\\Models\\User": 1}, "count": 119}, "livewire": {"data": {"type-pay #BGMZOUFWD9ezpAegoVhH": "array:5 [\n  \"data\" => array:15 [\n    \"currentPage\" => \"list\"\n    \"newPay\" => []\n    \"editPay\" => []\n    \"currentAnnee\" => null\n    \"sourceAnnee\" => null\n    \"prix\" => []\n    \"searchTerm\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"showReplicateModal\" => false\n    \"showGlobalReplicateModal\" => true\n    \"globalSourceAnnee\" => \"6\"\n    \"globalDestinationAnnee\" => \"7\"\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"type-pay\"\n  \"view\" => \"livewire.administration.typepay.index\"\n  \"component\" => \"App\\Http\\Livewire\\TypePay\"\n  \"id\" => \"BGMZOUFWD9ezpAegoVhH\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/typepay\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1757674325\n]"}, "request": {"path_info": "/livewire/message/type-pay", "status_code": "<pre class=sf-dump id=sf-dump-506214794 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-506214794\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1046497555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1046497555\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1089433657 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">BGMZOUFWD9ezpAegoVhH</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">type-pay</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">caisse/typepay</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">08330426</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>newPay</span>\" => []\n      \"<span class=sf-dump-key>editPay</span>\" => []\n      \"<span class=sf-dump-key>currentAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>prix</span>\" => []\n      \"<span class=sf-dump-key>searchTerm</span>\" => \"\"\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showReplicateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showGlobalReplicateModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>globalSourceAnnee</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>globalDestinationAnnee</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">dc6866ae2e70cd60a2908f24539c966d7fce777980a99177a39d6b47243799a6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mn4k</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"19 characters\">replicateAllTariffs</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089433657\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2066534995 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">686</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdBbk5VcndjMzYzaWNoV0preHJoUmc9PSIsInZhbHVlIjoiN20yQVptTWl5QWE3eTVPU2IvMWR0cmdoUlZEdURrUitXZndjaHNZUG9rWXA5RGN3NkxGbS9NV29FQnp4dXRBZXY1c0dxMkZnUHhwNklFRXgyK0lqaEpJakNTRWxFMkhCYjA0M3o4RW1sVHlla1V4aHd4U1V2KzI5MzVvR3ZSM0MiLCJtYWMiOiJkOTM3ZmFmZjJhMzI5YzIxMWFlMjUyMDRiMDFlYjY2MDBjNjlmNmVhMmQ0NGZjNzJmMzczOWE5MjBjOGUxNmI0IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InU4TlpjZnZHRUdNeHJ0aWxRbUoyOHc9PSIsInZhbHVlIjoidmJnQlNkSG9OWHFES2pFMXRhTHFTNnV4bjBuSkJmRUlDQW8zVWlUMUc3VFY5VEtpYUI2YlFsNGJmUXlEZDJEZS9qSEtBS0VDbEc1cnF6eUZ0bEhvNXZUMHNESWNrYzNsdnhqM0Y0NU4xcGZ1UzZlbU5URzIwRnk3cjVEQ0lGWEgiLCJtYWMiOiI2ZDQ1ZWI1MDk3YWNlOTMyYTJmNmY1YWJjOTA5NmFjYWJiYzJlM2YyMmY2MGViZjU2YWRkNTI1NTI2YWNkZGZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066534995\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-481953248 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55821</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">686</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">686</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdBbk5VcndjMzYzaWNoV0preHJoUmc9PSIsInZhbHVlIjoiN20yQVptTWl5QWE3eTVPU2IvMWR0cmdoUlZEdURrUitXZndjaHNZUG9rWXA5RGN3NkxGbS9NV29FQnp4dXRBZXY1c0dxMkZnUHhwNklFRXgyK0lqaEpJakNTRWxFMkhCYjA0M3o4RW1sVHlla1V4aHd4U1V2KzI5MzVvR3ZSM0MiLCJtYWMiOiJkOTM3ZmFmZjJhMzI5YzIxMWFlMjUyMDRiMDFlYjY2MDBjNjlmNmVhMmQ0NGZjNzJmMzczOWE5MjBjOGUxNmI0IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InU4TlpjZnZHRUdNeHJ0aWxRbUoyOHc9PSIsInZhbHVlIjoidmJnQlNkSG9OWHFES2pFMXRhTHFTNnV4bjBuSkJmRUlDQW8zVWlUMUc3VFY5VEtpYUI2YlFsNGJmUXlEZDJEZS9qSEtBS0VDbEc1cnF6eUZ0bEhvNXZUMHNESWNrYzNsdnhqM0Y0NU4xcGZ1UzZlbU5URzIwRnk3cjVEQ0lGWEgiLCJtYWMiOiI2ZDQ1ZWI1MDk3YWNlOTMyYTJmNmY1YWJjOTA5NmFjYWJiYzJlM2YyMmY2MGViZjU2YWRkNTI1NTI2YWNkZGZhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1757674443.2178</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1757674443</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481953248\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1465854557 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2823hTwl4XRMSeNF4WDLtgNNA485HJ3kRlWQ1nyZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465854557\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1519226810 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 12 Sep 2025 10:54:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJBSFZxakFLT3QwVzVKOUs4QlhJd2c9PSIsInZhbHVlIjoiWFhKR01URnA1ajU0d2owY0pEc0JZcy9aWXZVZG4xUDVjMHVhMjJ2aGVVVlpjeWZhOUNSd0ZsUkVGNk44UjZ1NzMwc0x3UFBtYWNRMEszU0tJYWc3cmducGp2N2gwUVRkTHpVMVlDeTJZK2ZleXpySHVVMkx5R2E4UlQrOFRuQlIiLCJtYWMiOiI3NmUyNDkzMDUyMjJlODllMWQ1MjQ2MGM1Y2UzMTIxZTQ3MDI3ZDQzYjU2N2UyOTk0Mjg3ODg5ZTdjNDc4Nzc1IiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:54:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IiswTlQ2MWczcEMwTk5wYmwyKzI3T3c9PSIsInZhbHVlIjoidEQ2WFVwVndERHh1ZERKK2dVWTlGcG9mUG9wK2dwaXhYZEE3WWk0UHRoZ21mYXhuNm5kWS9QUm1POE1XN0UrbTNxYnNKZXJnUUFxbWJ4djZWYUxtRlh4TkVERC9LMTU1OTdsMkwvT1lSTlIxUTRabXdkU1ZXcWd4V3owWnA0UWsiLCJtYWMiOiJmN2RmZGMxMDZiZjU5MTlhMjc5YTFmNTlhNmZlYWI5OTM4ZTc1YWRhZTM1OGYxNzkzMmEwMWUzMTM4Mzg0MjFhIiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:54:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJBSFZxakFLT3QwVzVKOUs4QlhJd2c9PSIsInZhbHVlIjoiWFhKR01URnA1ajU0d2owY0pEc0JZcy9aWXZVZG4xUDVjMHVhMjJ2aGVVVlpjeWZhOUNSd0ZsUkVGNk44UjZ1NzMwc0x3UFBtYWNRMEszU0tJYWc3cmducGp2N2gwUVRkTHpVMVlDeTJZK2ZleXpySHVVMkx5R2E4UlQrOFRuQlIiLCJtYWMiOiI3NmUyNDkzMDUyMjJlODllMWQ1MjQ2MGM1Y2UzMTIxZTQ3MDI3ZDQzYjU2N2UyOTk0Mjg3ODg5ZTdjNDc4Nzc1IiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:54:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IiswTlQ2MWczcEMwTk5wYmwyKzI3T3c9PSIsInZhbHVlIjoidEQ2WFVwVndERHh1ZERKK2dVWTlGcG9mUG9wK2dwaXhYZEE3WWk0UHRoZ21mYXhuNm5kWS9QUm1POE1XN0UrbTNxYnNKZXJnUUFxbWJ4djZWYUxtRlh4TkVERC9LMTU1OTdsMkwvT1lSTlIxUTRabXdkU1ZXcWd4V3owWnA0UWsiLCJtYWMiOiJmN2RmZGMxMDZiZjU5MTlhMjc5YTFmNTlhNmZlYWI5OTM4ZTc1YWRhZTM1OGYxNzkzMmEwMWUzMTM4Mzg0MjFhIiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:54:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519226810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1283550901 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757674325</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283550901\", {\"maxDepth\":0})</script>\n"}}