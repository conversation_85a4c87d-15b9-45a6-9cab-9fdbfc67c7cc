<?php

require_once 'vendor/autoload.php';

use App\Models\TypePayment;
use App\Models\Niveau;
use App\Models\AnneeUniversitaire;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Test de la fonctionnalité de copie des tarifs ===\n\n";

try {
    // Vérifier les données existantes
    echo "1. Vérification des données existantes...\n";
    
    $annees = AnneeUniversitaire::all();
    echo "Années universitaires disponibles:\n";
    foreach ($annees as $annee) {
        echo "  - ID: {$annee->id}, Nom: {$annee->nom}\n";
    }
    
    $niveaux = Niveau::all();
    echo "\nNiveaux disponibles:\n";
    foreach ($niveaux as $niveau) {
        echo "  - ID: {$niveau->id}, Nom: {$niveau->nom}\n";
    }
    
    $typePayments = TypePayment::all();
    echo "\nTypes de paiement disponibles:\n";
    foreach ($typePayments as $type) {
        echo "  - ID: {$type->id}, Nom: {$type->nom}\n";
    }
    
    // Créer des données de test si nécessaire
    echo "\n2. Création de données de test...\n";
    
    // Créer des années si elles n'existent pas
    $annee2023 = AnneeUniversitaire::firstOrCreate(['nom' => '2023/2024']);
    $annee2024 = AnneeUniversitaire::firstOrCreate(['nom' => '2024/2025']);
    
    echo "Années de test créées: {$annee2023->nom} (ID: {$annee2023->id}) et {$annee2024->nom} (ID: {$annee2024->id})\n";
    
    // Créer un niveau si il n'existe pas
    $niveau = Niveau::first();
    if (!$niveau) {
        $niveau = Niveau::create(['nom' => 'Licence 1', 'sigle' => 'L1']);
        echo "Niveau de test créé: {$niveau->nom} (ID: {$niveau->id})\n";
    } else {
        echo "Niveau existant utilisé: {$niveau->nom} (ID: {$niveau->id})\n";
    }
    
    // Créer un type de paiement si il n'existe pas
    $typePayment = TypePayment::first();
    if (!$typePayment) {
        $typePayment = TypePayment::create(['nom' => 'Frais de scolarité']);
        echo "Type de paiement de test créé: {$typePayment->nom} (ID: {$typePayment->id})\n";
    } else {
        echo "Type de paiement existant utilisé: {$typePayment->nom} (ID: {$typePayment->id})\n";
    }
    
    // Vérifier si la colonne annee_universitaire_id existe
    echo "\n3. Vérification de la structure de la table...\n";

    try {
        // Test simple pour voir si la colonne existe
        DB::select('SELECT annee_universitaire_id FROM niveau_type_payment LIMIT 1');
        echo "✓ La colonne annee_universitaire_id existe\n";
        $hasAnneeColumn = true;
    } catch (Exception $e) {
        echo "✗ La colonne annee_universitaire_id n'existe pas encore\n";
        echo "Vous devez exécuter: php artisan migrate\n";
        $hasAnneeColumn = false;
    }

    if ($hasAnneeColumn) {
        // Ajouter un tarif pour l'année 2023
        echo "\n4. Ajout d'un tarif de test pour l'année 2023...\n";

        // Supprimer les tarifs existants pour cette année
        $typePayment->niveau()->wherePivot('annee_universitaire_id', $annee2023->id)->detach();

        // Ajouter le nouveau tarif
        $typePayment->niveau()->attach($niveau->id, [
            'prix' => 500000,
            'annee_universitaire_id' => $annee2023->id
        ]);

        echo "Tarif ajouté: {$niveau->nom} = 500,000 MGA pour l'année {$annee2023->nom}\n";

        // Vérifier que le tarif a été ajouté
        $tarif = $typePayment->niveau()
            ->wherePivot('annee_universitaire_id', $annee2023->id)
            ->first();

        if ($tarif) {
            echo "✓ Tarif vérifié: {$tarif->pivot->prix} MGA\n";
        } else {
            echo "✗ Erreur: Tarif non trouvé\n";
            exit(1);
        }

        // Simuler la copie vers l'année 2024
        echo "\n5. Test de copie vers l'année 2024...\n";

        // Supprimer les tarifs existants pour l'année de destination
        $typePayment->niveau()->wherePivot('annee_universitaire_id', $annee2024->id)->detach();

        // Copier les tarifs
        $sourceTariffs = $typePayment->niveau()
            ->wherePivot('annee_universitaire_id', $annee2023->id)
            ->get();

        $attachData = [];
        foreach ($sourceTariffs as $niveau_tarif) {
            $attachData[$niveau_tarif->id] = [
                'prix' => $niveau_tarif->pivot->prix,
                'annee_universitaire_id' => $annee2024->id,
            ];
        }

        if (!empty($attachData)) {
            $typePayment->niveau()->attach($attachData);
            echo "✓ Tarifs copiés avec succès\n";
        } else {
            echo "✗ Aucun tarif à copier\n";
            exit(1);
        }

        // Vérifier la copie
        $tarifCopie = $typePayment->niveau()
            ->wherePivot('annee_universitaire_id', $annee2024->id)
            ->first();

        if ($tarifCopie && $tarifCopie->pivot->prix == 500000) {
            echo "✓ Copie vérifiée: {$tarifCopie->pivot->prix} MGA pour l'année {$annee2024->nom}\n";
        } else {
            echo "✗ Erreur: Copie échouée\n";
            exit(1);
        }
    } else {
        echo "\nPour tester la fonctionnalité complète, veuillez d'abord exécuter:\n";
        echo "php artisan migrate\n";
    }
    
    echo "\n=== Test réussi ! ===\n";
    echo "La fonctionnalité de copie des tarifs fonctionne correctement.\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
