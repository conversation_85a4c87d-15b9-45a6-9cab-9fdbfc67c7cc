{"__meta": {"id": "X2a6d202f4c230ed8d0748401d91166b5", "datetime": "2025-09-12 13:53:00", "utime": 1757674380.385504, "method": "POST", "uri": "/livewire/message/type-pay", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757674379.168961, "end": 1757674380.385534, "duration": 1.2165729999542236, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1757674379.168961, "relative_start": 0, "end": 1757674379.896951, "relative_end": 1757674379.896951, "duration": 0.727989912033081, "duration_str": "728ms", "params": [], "collector": null}, {"label": "Application", "start": 1757674379.898247, "relative_start": 0.7292859554290771, "end": 1757674380.385538, "relative_end": 4.0531158447265625e-06, "duration": 0.4872910976409912, "duration_str": "487ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26861984, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.typepay.index (\\resources\\views\\livewire\\administration\\typepay\\index.blade.php)", "param_count": 20, "params": ["pays", "annees", "livewireLayout", "errors", "_instance", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/index.blade.php&line=0"}, {"name": "livewire.administration.typepay.liste (\\resources\\views\\livewire\\administration\\typepay\\liste.blade.php)", "param_count": 22, "params": ["__env", "app", "errors", "_instance", "pays", "annees", "livewireLayout", "currentPage", "newPay", "editPay", "currentAnnee", "sourceAnnee", "prix", "searchTerm", "sortField", "sortDirection", "showReplicateModal", "showGlobalReplicateModal", "globalSourceAnnee", "globalDestinationAnnee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/typepay/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.08695, "accumulated_duration_str": "86.95ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00609, "duration_str": "6.09ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 7.004}, {"sql": "select count(*) as aggregate from `type_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03336, "duration_str": "33.36ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 7.004, "width_percent": 38.367}, {"sql": "select * from `type_payments` order by `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 45.371, "width_percent": 1.449}, {"sql": "select `niveaux`.*, `niveau_type_payment`.`type_payment_id` as `pivot_type_payment_id`, `niveau_type_payment`.`niveau_id` as `pivot_niveau_id`, `niveau_type_payment`.`prix` as `pivot_prix`, `niveau_type_payment`.`annee_universitaire_id` as `pivot_annee_universitaire_id` from `niveaux` inner join `niveau_type_payment` on `niveaux`.`id` = `niveau_type_payment`.`niveau_id` where `niveau_type_payment`.`type_payment_id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 83}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00211, "duration_str": "2.11ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:83", "connection": "imsaaapp", "start_percent": 46.82, "width_percent": 2.427}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\TypePay.php", "line": 87}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Livewire\\TypePay.php:87", "connection": "imsaaapp", "start_percent": 49.247, "width_percent": 1.254}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 50.5, "width_percent": 0.92}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 51.42, "width_percent": 0.828}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 52.248, "width_percent": 1.07}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 53.318, "width_percent": 1.449}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 54.767, "width_percent": 1.07}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 55.837, "width_percent": 0.989}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 56.826, "width_percent": 1.185}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 58.01, "width_percent": 0.874}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 58.884, "width_percent": 0.978}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 59.862, "width_percent": 0.955}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00233, "duration_str": "2.33ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 60.817, "width_percent": 2.68}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 63.496, "width_percent": 1.058}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0025099999999999996, "duration_str": "2.51ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 64.554, "width_percent": 2.887}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00401, "duration_str": "4.01ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 67.441, "width_percent": 4.612}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 72.053, "width_percent": 0.955}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 73.007, "width_percent": 0.863}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 73.87, "width_percent": 0.828}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 74.698, "width_percent": 1.438}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.015300000000000001, "duration_str": "15.3ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 76.136, "width_percent": 17.596}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 93.732, "width_percent": 0.805}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 94.537, "width_percent": 1.794}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00147, "duration_str": "1.47ms", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 96.331, "width_percent": 1.691}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 6 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 98.022, "width_percent": 1.07}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 7 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "e0a5746a8b8f1ed2c6248748474a62c214b44a85", "line": 84}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "view::e0a5746a8b8f1ed2c6248748474a62c214b44a85:84", "connection": "imsaaapp", "start_percent": 99.091, "width_percent": 0.909}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 31, "App\\Models\\Niveau": 77, "App\\Models\\TypePayment": 10, "App\\Models\\User": 1}, "count": 119}, "livewire": {"data": {"type-pay #BGMZOUFWD9ezpAegoVhH": "array:5 [\n  \"data\" => array:15 [\n    \"currentPage\" => \"list\"\n    \"newPay\" => []\n    \"editPay\" => []\n    \"currentAnnee\" => null\n    \"sourceAnnee\" => null\n    \"prix\" => []\n    \"searchTerm\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"showReplicateModal\" => false\n    \"showGlobalReplicateModal\" => true\n    \"globalSourceAnnee\" => null\n    \"globalDestinationAnnee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"type-pay\"\n  \"view\" => \"livewire.administration.typepay.index\"\n  \"component\" => \"App\\Http\\Livewire\\TypePay\"\n  \"id\" => \"BGMZOUFWD9ezpAegoVhH\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/typepay\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1757674325\n]"}, "request": {"path_info": "/livewire/message/type-pay", "status_code": "<pre class=sf-dump id=sf-dump-1513203985 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1513203985\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-560037788 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-560037788\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-824005824 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">BGMZOUFWD9ezpAegoVhH</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">type-pay</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">caisse/typepay</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">8d69777a</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>newPay</span>\" => []\n      \"<span class=sf-dump-key>editPay</span>\" => []\n      \"<span class=sf-dump-key>currentAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>prix</span>\" => []\n      \"<span class=sf-dump-key>searchTerm</span>\" => \"\"\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showReplicateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showGlobalReplicateModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>globalSourceAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>globalDestinationAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">bc6e7cf57a6126d84d0482e368469c68838958a8d3aa71b854043238c35fd935</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">51h</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"24 characters\">openGlobalReplicateModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824005824\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1084749106 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">692</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBjUnlmVXllbmJlcjdVWXQ0a0IzZFE9PSIsInZhbHVlIjoiZW5OUyt3NStUOFJQOXkrayt4bzArZ0w0ekI2TnJLYmdSWFZvdTNWNmVyT1AwbExyY1lmTW5lTmI3NFZ6YzNwYnNleEFqMEZMMG5SWUxrcTdZUCtEV2hSQlVXUUVPNFB5VU5GYVlQa2VDendBUkdaeE1KSlZGSHoxTFQvb0hCM0EiLCJtYWMiOiIyN2JlNTIxNWVmNjE4MjM3YTQwZjZlZDIxNmRkY2QwYmIwZTk3OTIzM2Y5MWJlYTNjZWMwNzg4MDY5Y2QyMzM2IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im42azlMUzJVNzFJOE04YnN5NzQwRnc9PSIsInZhbHVlIjoiRlA1ODhDekR3RE41ck1FVEdxSHNGOEE0OWJNYkZ3bGYvMm0rN1NacytTU25tZGYvYmYyTTk1Q3FiVzdwL2JPVE15czk0SVZ6Q3ErWHVqd2tzOXMvTDFCTitnZi92LzBOVUpoRVd2OERNSWtwT1hWOHJuTU0rMVNDUEhTZzZkWlciLCJtYWMiOiJiOWRkMjdkMWE0NzYwMTkwZjhiNGM1YzVhNjk5Y2QzODRjYzY4MWNkMjBhOWU5N2IzYmI1M2Q4MGNhMjhmMjRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084749106\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1546114296 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55736</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/type-pay</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">692</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">692</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBjUnlmVXllbmJlcjdVWXQ0a0IzZFE9PSIsInZhbHVlIjoiZW5OUyt3NStUOFJQOXkrayt4bzArZ0w0ekI2TnJLYmdSWFZvdTNWNmVyT1AwbExyY1lmTW5lTmI3NFZ6YzNwYnNleEFqMEZMMG5SWUxrcTdZUCtEV2hSQlVXUUVPNFB5VU5GYVlQa2VDendBUkdaeE1KSlZGSHoxTFQvb0hCM0EiLCJtYWMiOiIyN2JlNTIxNWVmNjE4MjM3YTQwZjZlZDIxNmRkY2QwYmIwZTk3OTIzM2Y5MWJlYTNjZWMwNzg4MDY5Y2QyMzM2IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im42azlMUzJVNzFJOE04YnN5NzQwRnc9PSIsInZhbHVlIjoiRlA1ODhDekR3RE41ck1FVEdxSHNGOEE0OWJNYkZ3bGYvMm0rN1NacytTU25tZGYvYmYyTTk1Q3FiVzdwL2JPVE15czk0SVZ6Q3ErWHVqd2tzOXMvTDFCTitnZi92LzBOVUpoRVd2OERNSWtwT1hWOHJuTU0rMVNDUEhTZzZkWlciLCJtYWMiOiJiOWRkMjdkMWE0NzYwMTkwZjhiNGM1YzVhNjk5Y2QzODRjYzY4MWNkMjBhOWU5N2IzYmI1M2Q4MGNhMjhmMjRhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1757674379.169</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1757674379</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546114296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2028283928 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2823hTwl4XRMSeNF4WDLtgNNA485HJ3kRlWQ1nyZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028283928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1830383050 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 12 Sep 2025 10:53:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjZ4VFdSTTZINXQrWCtCN1VWR1Y1K3c9PSIsInZhbHVlIjoiWDVxVEJqSTR5dEtMNW1TTnNXK29INVRBNHYwN3B6bEUvUVlrNHZ1ditNYUkxVUNBVVJCaXBtaDhVaE1kUUdNYkt4dGVzY3FEeGtWeVlod1hjUmZIeFF5Mldmdk40UjRHYlk3STZESGhYdzRINzVQM1VleXh2OTdmclpGZUw5cnoiLCJtYWMiOiJiZTA4M2JhNmQ4MzQ2NWQzOWUyOGZmZjNiYWIyMDIxODQ1MjE4MDFhZGE4MjgzZTgwYmMzY2ZkMjY4OTIzNWMyIiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:53:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkxkemIvQmpQdWZiOEJicFhzQVhJa0E9PSIsInZhbHVlIjoiYldxUzZHTDlOdHZKUWdzZWlIRGU2dUJ1ajBmbFFvbXk0bGJqMXE2anRMU21uOFU1cm03a04xWkZWZC8yZ2xxSnpURlhjMjVQYVU5ZTNrd1o1WFIvVGRSdTJGWG91RitYWVI1T0hrZnVSVXV0WWNBNFJhNzg1VTNBMkxuK0NJZzciLCJtYWMiOiJmNjRlZTUyODMwMjNmZjgxOGI3NzUxZGRiOTg5NjU0YmRiY2IzZWE4ZTQwNWVlMjU1YzZhMGJiMTY0YzQwZWU5IiwidGFnIjoiIn0%3D; expires=Fri, 12 Sep 2025 12:53:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjZ4VFdSTTZINXQrWCtCN1VWR1Y1K3c9PSIsInZhbHVlIjoiWDVxVEJqSTR5dEtMNW1TTnNXK29INVRBNHYwN3B6bEUvUVlrNHZ1ditNYUkxVUNBVVJCaXBtaDhVaE1kUUdNYkt4dGVzY3FEeGtWeVlod1hjUmZIeFF5Mldmdk40UjRHYlk3STZESGhYdzRINzVQM1VleXh2OTdmclpGZUw5cnoiLCJtYWMiOiJiZTA4M2JhNmQ4MzQ2NWQzOWUyOGZmZjNiYWIyMDIxODQ1MjE4MDFhZGE4MjgzZTgwYmMzY2ZkMjY4OTIzNWMyIiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:53:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkxkemIvQmpQdWZiOEJicFhzQVhJa0E9PSIsInZhbHVlIjoiYldxUzZHTDlOdHZKUWdzZWlIRGU2dUJ1ajBmbFFvbXk0bGJqMXE2anRMU21uOFU1cm03a04xWkZWZC8yZ2xxSnpURlhjMjVQYVU5ZTNrd1o1WFIvVGRSdTJGWG91RitYWVI1T0hrZnVSVXV0WWNBNFJhNzg1VTNBMkxuK0NJZzciLCJtYWMiOiJmNjRlZTUyODMwMjNmZjgxOGI3NzUxZGRiOTg5NjU0YmRiY2IzZWE4ZTQwNWVlMjU1YzZhMGJiMTY0YzQwZWU5IiwidGFnIjoiIn0%3D; expires=Fri, 12-Sep-2025 12:53:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830383050\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1343193117 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0vp6Hh3GF395MpFR2G2WTQpT3o3A4NnxVLPaLpl0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/caisse/typepay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757674325</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343193117\", {\"maxDepth\":0})</script>\n"}}